import 'package:flutter/material.dart';

/// Simple & Beautiful Theme - نظام تصميم بسيط وجميل
class SimpleTheme {
  // Simple Colors - ألوان بسيطة وجميلة
  static const Color darkBg = Color(0xFF1F1D2B);
  static const Color cardBg = Color(0xFF2A2D3E);
  static const Color surfaceBg = Color(0xFF2A2A2A);
  static const Color surfaceLight = Color(0xFF3A3A3A);

  static const Color primary = Color(0xFF6366f1);
  static const Color primaryLight = Color(0xFF8B5CF6);
  static const Color primaryDark = Color(0xFF4F46E5);
  static const Color secondary = Color(0xFF4CAF50);
  static const Color accent = Color(0xFFFF9800);

  static const Color success = Color(0xFF4CAF50);
  static const Color danger = Color(0xFFF44336);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);

  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB0B0B0);
  static const Color textMuted = Color(0xFF808080);
  static const Color textDisabled = Color(0xFF666666);
  static const Color textOnPrimary = Color(0xFFFFFFFF);

  // Border colors
  static const Color borderDark = Color(0xFF3A3A3A);
  static const Color borderLight = Color(0xFFE0E0E0);

  // Additional colors for compatibility
  static const Color accentPink = Color(0xFFE91E63);
  static const Color accentGreen = Color(0xFF4CAF50);
  static const Color accentOrange = Color(0xFFFF9800);
  static const Color accentTeal = Color(0xFF009688);
  static const Color primaryBlue = primary; // Alias for compatibility
  static const Color primaryPurple = Color(0xFF8B5CF6);

  // Light variants for compatibility
  static const Color primaryBlueDark = Color(0xFF4F46E5);
  static const Color primaryPurpleLight = Color(0xFFA78BFA);
  static const Color accentPinkLight = Color(0xFFF48FB1);
  static const Color successLight = Color(0xFF81C784);
  static const Color warningLight = Color(0xFFFFB74D);

  // Simple Gradients - تدرجات بسيطة
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardGradient = LinearGradient(
    colors: [cardBg, Color(0xFF252836)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [darkBg, cardBg],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient primaryGradientReverse = LinearGradient(
    colors: [primaryLight, primary],
    begin: Alignment.bottomRight,
    end: Alignment.topLeft,
  );

  static const LinearGradient modernGradient = LinearGradient(
    colors: [Color(0xFF6366f1), Color(0xFF8B5CF6)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Simple Shadows - ظلال بسيطة
  static const List<BoxShadow> cardShadow = [
    BoxShadow(color: Color(0x1A000000), blurRadius: 8, offset: Offset(0, 2)),
  ];

  static const List<BoxShadow> buttonShadow = [
    BoxShadow(color: Color(0x26000000), blurRadius: 4, offset: Offset(0, 2)),
  ];

  // Simple Decorations - زخارف بسيطة
  static BoxDecoration get cardDecoration => BoxDecoration(
    gradient: cardGradient,
    borderRadius: BorderRadius.circular(12),
    boxShadow: cardShadow,
  );

  static BoxDecoration get buttonDecoration => BoxDecoration(
    gradient: primaryGradient,
    borderRadius: BorderRadius.circular(8),
    boxShadow: buttonShadow,
  );

  static BoxDecoration get premiumCard => BoxDecoration(
    gradient: cardGradient,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: const Color(0xFF3A3A3A), width: 1),
    boxShadow: cardShadow,
  );

  // Button decorations
  static BoxDecoration get primaryButton => BoxDecoration(
    gradient: primaryGradient,
    borderRadius: BorderRadius.circular(8),
    boxShadow: buttonShadow,
  );

  static BoxDecoration get secondaryButton => BoxDecoration(
    color: surfaceBg,
    borderRadius: BorderRadius.circular(8),
    border: Border.all(color: Colors.white.withValues(alpha: 0.2), width: 1),
    boxShadow: cardShadow,
  );

  static BoxDecoration get dangerButton => BoxDecoration(
    color: danger,
    borderRadius: BorderRadius.circular(8),
    boxShadow: buttonShadow,
  );

  static BoxDecoration get successButton => BoxDecoration(
    color: success,
    borderRadius: BorderRadius.circular(8),
    boxShadow: buttonShadow,
  );

  // Theme Data
  static ThemeData get darkTheme => ThemeData(
    brightness: Brightness.dark,
    primarySwatch: Colors.blue,
    primaryColor: primary,
    scaffoldBackgroundColor: darkBg,
    cardColor: cardBg,
    dividerColor: surfaceBg,

    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      iconTheme: IconThemeData(color: textPrimary),
      titleTextStyle: TextStyle(
        color: textPrimary,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),

    cardTheme: CardThemeData(
      color: cardBg,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primary,
        foregroundColor: textPrimary,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    ),

    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: cardBg,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
      contentPadding: const EdgeInsets.all(16),
    ),

    colorScheme: const ColorScheme.dark(
      primary: primary,
      secondary: secondary,
      surface: cardBg,
      error: danger,
    ),
  );

  static ThemeData get lightTheme => ThemeData(
    brightness: Brightness.light,
    primarySwatch: Colors.blue,
    primaryColor: primary,
    scaffoldBackgroundColor: Colors.white,
    cardColor: const Color(0xFFF8F9FA),

    colorScheme: const ColorScheme.light(
      primary: primary,
      secondary: secondary,
      surface: Color(0xFFF8F9FA),
      error: danger,
      onSurface: Colors.black87,
      onPrimary: Colors.white,
    ),

    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      iconTheme: IconThemeData(color: Colors.white),
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),

    cardTheme: CardThemeData(
      color: const Color(0xFFF8F9FA),
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    ),

    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: Colors.white),
      bodyMedium: TextStyle(color: Colors.white),
      titleLarge: TextStyle(color: Colors.white),
      titleMedium: TextStyle(color: Colors.white),
    ),
  );

  // Helper methods to get theme-aware colors
  static Color getBackgroundColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkBg
        : Colors.white;
  }

  static Color getCardColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? cardBg
        : const Color(0xFFF8F9FA);
  }

  static Color getTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? textPrimary
        : Colors.black;
  }

  static Color getSecondaryTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? textSecondary
        : Colors.black.withValues(alpha: 0.7);
  }

  static Color getBorderColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? borderDark
        : borderLight;
  }

  static Color getIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark ? primary : primary;
  }

  static Color getSubtitleColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? SimpleTheme.getSecondaryTextColor(context)
        : Colors.black.withValues(alpha: 0.6);
  }

  static Color getContainerColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF252836)
        : const Color(0xFFE9ECEF);
  }

  static LinearGradient getBackgroundGradient(BuildContext context) {
    return LinearGradient(
      colors: Theme.of(context).brightness == Brightness.dark
          ? [const Color(0xFF2A2D3E), const Color(0xFF1F1D2B)]
          : [const Color(0xFFF8F9FA), Colors.white],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );
  }

  static LinearGradient getCardGradient(BuildContext context) {
    return LinearGradient(
      colors: Theme.of(context).brightness == Brightness.dark
          ? [const Color(0xFF2A2D3E), const Color(0xFF252836)]
          : [const Color(0xFFF8F9FA), const Color(0xFFE9ECEF)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }
}

/// Performance-optimized animations - رسوم متحركة محسنة للأداء
class SimpleAnimations {
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 250);
  static const Duration slow = Duration(milliseconds: 400);

  static const Curve easeOut = Curves.easeOut;
  static const Curve easeIn = Curves.easeIn;
  static const Curve bounce = Curves.elasticOut;

  // Simple fade animation
  static Widget fadeIn({
    required Widget child,
    Duration duration = normal,
    double begin = 0.0,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: begin, end: end),
      curve: easeOut,
      builder: (context, value, child) {
        return Opacity(opacity: value, child: child);
      },
      child: child,
    );
  }

  // Simple scale animation
  static Widget scaleIn({
    required Widget child,
    Duration duration = normal,
    double begin = 0.8,
    double end = 1.0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween(begin: begin, end: end),
      curve: bounce,
      builder: (context, value, child) {
        return Transform.scale(scale: value, child: child);
      },
      child: child,
    );
  }

  // Simple slide animation
  static Widget slideIn({
    required Widget child,
    Duration duration = normal,
    Offset begin = const Offset(0, 0.2),
    Offset end = Offset.zero,
  }) {
    return TweenAnimationBuilder<Offset>(
      duration: duration,
      tween: Tween(begin: begin, end: end),
      curve: easeOut,
      builder: (context, value, child) {
        return Transform.translate(offset: value, child: child);
      },
      child: child,
    );
  }
}

/// Performance utilities - أدوات تحسين الأداء
class PerformanceHelper {
  static bool isLowEndDevice() {
    // Simple check for low-end devices
    return false; // يمكن تحسينه لاحقاً
  }

  static Duration getAnimationDuration() {
    return isLowEndDevice() ? SimpleAnimations.fast : SimpleAnimations.normal;
  }

  static Widget optimizedBuilder({
    required Widget Function() builder,
    Widget? fallback,
  }) {
    try {
      return builder();
    } catch (e) {
      return fallback ?? const SizedBox.shrink();
    }
  }
}
