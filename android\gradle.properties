# تحسين أداء Gradle حسب إمكانيات الجهاز
org.gradle.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dkotlin.daemon.jvm.options=-Xmx1G
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.workers.max=2
# إزالة تحديد منفذ الديمون لتجنب مشاكل الاتصال
# org.gradle.daemon.port=50060

# AndroidX
android.useAndroidX=true
android.enableJetifier=true

# R8 و D8 لتقليل الحجم بشكل قوي
android.enableR8.fullMode=true
android.enableD8.desugaring=true
android.enableIncrementalDesugaring=true
android.enableR8=true

# تحسينات إضافية لتقليل الحجم
android.enableResourceOptimizations=true
android.enableBuildCache=true
android.enableSeparateAnnotationProcessing=true

# تحسين الذاكرة والأداء
android.enableParallelGc=true
android.enableIncrementalJavaCompile=true

# تقليل حجم الموارد
android.aapt2.optimizeResourceShrinking=true
