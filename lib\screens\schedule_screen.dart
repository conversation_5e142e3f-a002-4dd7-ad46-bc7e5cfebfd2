import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import '../providers/app_provider.dart';
import '../models/lesson.dart';
import '../models/group.dart';
import '../theme/simple_theme.dart';

import 'schedule_settings_screen.dart';

class ScheduleScreen extends StatefulWidget {
  const ScheduleScreen({super.key});

  @override
  State<ScheduleScreen> createState() => _ScheduleScreenState();
}

class _ScheduleScreenState extends State<ScheduleScreen>
    with TickerProviderStateMixin {
  DateTime selectedDate = DateTime.now();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String _selectedView = 'day'; // day, week, month

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section
                _buildHeader(context, provider),
                const SizedBox(height: 24),

                // View Selector
                _buildViewSelector(),
                const SizedBox(height: 20),

                // Date Navigation
                _buildDateNavigation(),
                const SizedBox(height: 20),

                // Schedule Content
                _buildScheduleContent(provider),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء الهيدر
  Widget _buildHeader(BuildContext context, AppProvider provider) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            SimpleTheme.primaryBlue.withValues(alpha: 0.1),
            SimpleTheme.accentPink.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: SimpleTheme.getBorderColor(context),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الجدول الزمني',
                  style: GoogleFonts.cairo(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: SimpleTheme.getTextColor(context),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'إدارة وعرض الدروس والمواعيد',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: SimpleTheme.getSecondaryTextColor(context),
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  gradient: SimpleTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: SimpleTheme.primaryBlue.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () => _openScheduleSettings(context),
                  icon: Icon(
                    Icons.settings,
                    color: SimpleTheme.getTextColor(context),
                    size: 20,
                  ),
                  tooltip: 'إعدادات الجدول',
                ),
              ),
              const SizedBox(width: 12),
              Container(
                decoration: BoxDecoration(
                  color: SimpleTheme.getContainerColor(context),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: SimpleTheme.getBorderColor(context),
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () => _selectDate(context),
                  icon: Icon(
                    Icons.calendar_today,
                    color: SimpleTheme.getIconColor(context),
                    size: 20,
                  ),
                  tooltip: 'اختيار التاريخ',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء محدد العرض
  Widget _buildViewSelector() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: SimpleTheme.getContainerColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: SimpleTheme.getBorderColor(context),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          _buildViewButton('يوم', 'day'),
          _buildViewButton('أسبوع', 'week'),
          _buildViewButton('شهر', 'month'),
        ],
      ),
    );
  }

  Widget _buildViewButton(String title, String view) {
    final isSelected = _selectedView == view;
    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedView = view),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            gradient: isSelected ? SimpleTheme.primaryGradient : null,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
        ),
      ),
    );
  }

  // بناء التنقل بين التواريخ
  Widget _buildDateNavigation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: SimpleTheme.getContainerColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: SimpleTheme.getBorderColor(context),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () => _changeDate(-1),
            icon: Icon(
              Icons.arrow_back_ios,
              color: SimpleTheme.getIconColor(context),
            ),
          ),
          Text(
            DateFormat('EEEE، d MMMM yyyy', 'ar').format(selectedDate),
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: SimpleTheme.getTextColor(context),
            ),
          ),
          IconButton(
            onPressed: () => _changeDate(1),
            icon: Icon(
              Icons.arrow_forward_ios,
              color: SimpleTheme.getIconColor(context),
            ),
          ),
        ],
      ),
    );
  }

  // بناء محتوى الجدول
  Widget _buildScheduleContent(AppProvider provider) {
    switch (_selectedView) {
      case 'day':
        return _buildDayView(provider);
      case 'week':
        return _buildWeekView(provider);
      case 'month':
        return _buildMonthView(provider);
      default:
        return _buildDayView(provider);
    }
  }

  Widget _buildDayView(AppProvider provider) {
    // التحقق من وجود مجموعات قبل عرض الدروس
    if (provider.groups.isEmpty) {
      return _buildEmptyState();
    }

    final lessons = provider.lessons
        .where(
          (lesson) =>
              lesson.dateTime.year == selectedDate.year &&
              lesson.dateTime.month == selectedDate.month &&
              lesson.dateTime.day == selectedDate.day,
        )
        .toList();

    lessons.sort((a, b) => a.dateTime.compareTo(b.dateTime));

    if (lessons.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: lessons.map((lesson) {
        Group? group;
        try {
          group = provider.groups.firstWhere((g) => g.id == lesson.groupId);
        } catch (e) {
          if (provider.groups.isNotEmpty) {
            group = provider.groups.first;
          }
        }

        // التحقق من وجود المجموعة
        if (group == null) {
          return const SizedBox(height: 0);
        }
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildLessonCard(lesson, group, provider),
        );
      }).toList(),
    );
  }

  Widget _buildWeekView(AppProvider provider) {
    // تنفيذ عرض الأسبوع
    return _buildDayView(provider); // مؤقتاً
  }

  Widget _buildMonthView(AppProvider provider) {
    // تنفيذ عرض الشهر
    return _buildDayView(provider); // مؤقتاً
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: SimpleTheme.getContainerColor(context),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.event_note,
              size: 64,
              color: SimpleTheme.getSecondaryTextColor(
                context,
              ).withValues(alpha: 0.5),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد دروس في هذا اليوم',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: SimpleTheme.getSecondaryTextColor(context),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'يمكنك إضافة درس جديد من إعدادات الجدول',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: SimpleTheme.getSubtitleColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLessonCard(Lesson lesson, dynamic group, AppProvider provider) {
    // التحقق من وجود المجموعة
    if (provider.groups.isEmpty) {
      return const SizedBox(); // لا توجد مجموعات، نعرض مساحة فارغة
    }
    final now = DateTime.now();
    final isPast = lesson.dateTime.isBefore(now);
    final isToday =
        lesson.dateTime.day == now.day &&
        lesson.dateTime.month == now.month &&
        lesson.dateTime.year == now.year;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            isPast
                ? Colors.grey.withValues(alpha: 0.1)
                : isToday
                ? SimpleTheme.primaryBlue.withValues(alpha: 0.1)
                : SimpleTheme.getContainerColor(context),
            isPast
                ? Colors.grey.withValues(alpha: 0.05)
                : isToday
                ? SimpleTheme.accentPink.withValues(alpha: 0.1)
                : Colors.white.withValues(alpha: 0.02),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isPast
              ? Colors.grey.withValues(alpha: 0.2)
              : isToday
              ? SimpleTheme.primaryBlue.withValues(alpha: 0.3)
              : SimpleTheme.getBorderColor(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      group.name,
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isPast
                            ? SimpleTheme.getSubtitleColor(context)
                            : SimpleTheme.getTextColor(context),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      group.subject,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: isPast
                            ? Colors.white.withValues(alpha: 0.4)
                            : SimpleTheme.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: lesson.isCompleted
                      ? Colors.green.withValues(alpha: 0.2)
                      : isPast
                      ? Colors.red.withValues(alpha: 0.2)
                      : SimpleTheme.primaryBlue.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  lesson.isCompleted
                      ? 'مكتمل'
                      : isPast
                      ? 'فائت'
                      : 'قادم',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: lesson.isCompleted
                        ? Colors.green
                        : isPast
                        ? Colors.red
                        : SimpleTheme.primaryBlue,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: SimpleTheme.getSecondaryTextColor(context),
              ),
              const SizedBox(width: 8),
              Text(
                lesson.endTime != null
                    ? '${DateFormat('HH:mm').format(lesson.dateTime)} - ${DateFormat('HH:mm').format(lesson.endTime!)}'
                    : DateFormat('HH:mm').format(lesson.dateTime),
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
              const SizedBox(width: 24),
              Icon(
                Icons.people,
                size: 16,
                color: SimpleTheme.getSecondaryTextColor(context),
              ),
              const SizedBox(width: 8),
              Text(
                '${lesson.attendedStudentIds.length} طالب',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: SimpleTheme.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
          if (lesson.notes.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              lesson.notes,
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: SimpleTheme.getSecondaryTextColor(context),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // تغيير التاريخ
  void _changeDate(int days) {
    setState(() {
      selectedDate = selectedDate.add(Duration(days: days));
    });
  }

  // اختيار التاريخ
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: const Color(0xFF6366f1),
              onPrimary: SimpleTheme.getTextColor(context),
              surface: const Color(0xFF1e293b),
              onSurface: SimpleTheme.getTextColor(context),
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  // فتح إعدادات الجدول
  void _openScheduleSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ScheduleSettingsScreen()),
    );
  }
}
