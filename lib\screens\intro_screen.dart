import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import '../theme/simple_theme.dart';
import '../providers/app_provider.dart';
import 'splash_screen.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<IntroPageData> _pages = [
    IntroPageData(
      title: 'أهلاً وسهلاً في EduTrack',
      subtitle: 'رحلتك التعليمية الذكية تبدأ من هنا',
      description:
          'منصة إدارة التعليم الأكثر تطوراً هتساعدك تنظم طلابك ومجموعاتك بأحدث التقنيات الذكية',
      icon: Icons.school_rounded,
      color: SimpleTheme.primary,
    ),
    IntroPageData(
      title: 'إدارة المجموعات الذكية',
      subtitle: 'نظم طلابك بأحدث التقنيات',
      description:
          'اعمل مجموعات ديناميكية للطلاب مع نظام تتبع ذكي يحلل الأداء ويقترح التحسينات تلقائياً',
      icon: Icons.groups_rounded,
      color: SimpleTheme.secondary,
    ),
    IntroPageData(
      title: 'جدولة متطورة ومرنة',
      subtitle: 'خطط دروسك بذكاء وإبداع',
      description:
          'نظام جدولة متقدم مع تذكيرات ذكية وتحليل أوقات الذروة لتحسين الأداء التعليمي',
      icon: Icons.calendar_today_rounded,
      color: SimpleTheme.accent,
    ),
    IntroPageData(
      title: 'لوحة تحكم تفاعلية',
      subtitle: 'تحليلات متقدمة ورؤى عميقة',
      description:
          'احصل على رؤى عميقة وتحليلات متطورة لأداء طلابك مع توقعات ذكية للنتائج المستقبلية',
      icon: Icons.dashboard_rounded,
      color: SimpleTheme.success,
    ),
    IntroPageData(
      title: 'انطلق نحو المستقبل',
      subtitle: 'كل شيء جاهز للإبداع!',
      description:
          'الآن أصبح بإمكانك الاستفادة من أقوى منصة تعليمية ذكية مع تجربة مستخدم استثنائية ومميزات لا محدودة',
      icon: Icons.rocket_launch_rounded,
      color: SimpleTheme.info,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
    HapticFeedback.selectionClick();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: SimpleTheme.getBackgroundColor(context),
        body: SafeArea(
          child: Column(
            children: [
              // Top navigation
              _buildTopNavigation(),

              // Page content
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: _onPageChanged,
                  itemCount: _pages.length,
                  itemBuilder: (context, index) {
                    return _buildSimplePage(_pages[index], index);
                  },
                ),
              ),

              // Bottom navigation
              _buildBottomNavigation(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopNavigation() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Skip button
          GestureDetector(
            onTap: _startApp,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: SimpleTheme.getContainerColor(context),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: SimpleTheme.getBorderColor(context),
                  width: 1,
                ),
              ),
              child: Text(
                'تخطي',
                style: GoogleFonts.cairo(
                  color: SimpleTheme.getSecondaryTextColor(context),
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          // Page indicator
          Row(
            children: List.generate(
              _pages.length,
              (index) => AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: _currentPage == index ? 30 : 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _currentPage == index
                      ? SimpleTheme.primary
                      : SimpleTheme.getSecondaryTextColor(
                          context,
                        ).withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),

          // Theme toggle button
          Consumer<AppProvider>(
            builder: (context, provider, child) {
              return GestureDetector(
                onTap: () {
                  provider.toggleTheme();
                  HapticFeedback.lightImpact();
                },
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: SimpleTheme.getContainerColor(context),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: SimpleTheme.getBorderColor(context),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    provider.isDarkMode ? Icons.light_mode : Icons.dark_mode,
                    color: SimpleTheme.getIconColor(context),
                    size: 20,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSimplePage(IntroPageData page, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon container
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: page.color.withValues(alpha: 0.1),
              border: Border.all(
                color: page.color.withValues(alpha: 0.3),
                width: 2,
              ),
            ),
            child: Icon(page.icon, size: 60, color: page.color),
          ),
          const SizedBox(height: 40),

          // Title
          Text(
            page.title,
            style: GoogleFonts.cairo(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: SimpleTheme.getTextColor(context),
              height: 1.2,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Subtitle
          Text(
            page.subtitle,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: page.color,
              height: 1.3,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),

          // Description
          Text(
            page.description,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: Colors.white.withValues(alpha: 0.8),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Previous button
          if (_currentPage > 0)
            GestureDetector(
              onTap: _previousPage,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: SimpleTheme.getCardColor(context),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.arrow_back_ios_rounded,
                  color: Colors.white.withValues(alpha: 0.8),
                  size: 20,
                ),
              ),
            )
          else
            const SizedBox(width: 48),

          // Next/Start button
          GestureDetector(
            onTap: _currentPage == _pages.length - 1 ? _startApp : _nextPage,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [SimpleTheme.primary, SimpleTheme.primaryLight],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: SimpleTheme.primary.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _currentPage == _pages.length - 1 ? 'ابدأ الآن' : 'التالي',
                    style: GoogleFonts.cairo(
                      color: SimpleTheme.getTextColor(context),
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    _currentPage == _pages.length - 1
                        ? Icons.rocket_launch_rounded
                        : Icons.arrow_forward_ios_rounded,
                    color: SimpleTheme.getTextColor(context),
                    size: 18,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      HapticFeedback.lightImpact();
    }
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      HapticFeedback.lightImpact();
    }
  }

  void _startApp() async {
    HapticFeedback.mediumImpact();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('hasSeenIntro', true);

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const SplashScreen()),
      );
    }
  }
}

// Data class for intro pages
class IntroPageData {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color color;

  IntroPageData({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.color,
  });
}
